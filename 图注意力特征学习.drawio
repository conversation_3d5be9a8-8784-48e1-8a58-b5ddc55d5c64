<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <mxCell id="2" value="图注意力特征学习机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="200" height="30" as="geometry" />
        </mxCell>

        <mxCell id="3" value="异构图输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="4" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="150" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="150" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="200" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="200" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="8" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="270" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="270" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="11" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="4" target="8">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="5" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="4" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="6" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="15" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="235" as="sourcePoint" />
            <mxPoint x="280" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="16" value="GATv2 注意力机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="80" width="140" height="40" as="geometry" />
        </mxCell>

        <mxCell id="17" value="注意力权重计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="140" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="18" value="特征拼接&#xa;[hᵢ || hⱼ || eᵢⱼ]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="300" y="180" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="19" value="线性变换&#xa;Wᵣ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="390" y="180" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="20" value="LeakyReLU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="300" y="230" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="21" value="Softmax" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="380" y="230" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="22" value="αᵢⱼ⁽ʳ⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="270" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="23" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="17" target="18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="24" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="18" target="20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="25" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="19" target="21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="26" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="21" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="27" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="235" as="sourcePoint" />
            <mxPoint x="560" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="28" value="多头注意力聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="80" width="140" height="40" as="geometry" />
        </mxCell>

        <mxCell id="29" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="580" y="140" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="30" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="630" y="140" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="31" value="Head 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="680" y="140" width="40" height="25" as="geometry" />
        </mxCell>

        <mxCell id="32" value="h₁⁽ˡ⁺¹'¹⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="585" y="180" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="33" value="h₁⁽ˡ⁺¹'²⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="635" y="180" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="34" value="h₁⁽ˡ⁺¹'³⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="685" y="180" width="30" height="20" as="geometry" />
        </mxCell>

        <mxCell id="35" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="620" y="220" width="50" height="25" as="geometry" />
        </mxCell>

        <mxCell id="36" value="h₁⁽ˡ⁺¹⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="625" y="260" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="37" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="29" target="32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="38" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="30" target="33">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="39" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="31" target="34">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="40" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="32" target="35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="41" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="33" target="35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="42" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="34" target="35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="43" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="35" target="36">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="44" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="760" y="235" as="sourcePoint" />
            <mxPoint x="840" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="45" value="信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="80" width="140" height="40" as="geometry" />
        </mxCell>

        <mxCell id="46" value="可靠性评估网络" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="860" y="140" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="47" value="ReLU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="860" y="180" width="40" height="25" as="geometry" />
        </mxCell>

        <mxCell id="48" value="Sigmoid" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="910" y="180" width="50" height="25" as="geometry" />
        </mxCell>

        <mxCell id="49" value="ρᵢ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="885" y="220" width="30" height="25" as="geometry" />
        </mxCell>

        <mxCell id="50" value="自适应缩放&#xa;αₗ + (1-αₗ)·ρᵢ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="860" y="260" width="100" height="35" as="geometry" />
        </mxCell>

        <mxCell id="51" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="46" target="47">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="52" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="47" target="48">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="53" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="48" target="49">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="54" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="49" target="50">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="55" value="调节后特征&#xa;h⁽ˡ'ᵃᵈʲᵘˢᵗᵉᵈ⁾" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="320" width="100" height="40" as="geometry" />
        </mxCell>

        <mxCell id="56" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="50" target="55">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="400" as="sourcePoint" />
            <mxPoint x="1050" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="57" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="40" height="20" as="geometry" />
        </mxCell>

        <mxCell id="58" value="天线节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="430" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="59" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="130" y="430" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="60" value="注意力权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="210" y="430" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="61" value="特征向量" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="430" width="60" height="25" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
