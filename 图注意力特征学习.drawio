<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="图注意力特征学习机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- 输入层：异构图结构 -->
        <mxCell id="input_section" value="异构图输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 天线节点 -->
        <mxCell id="antenna1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="150" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="150" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="200" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="200" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- 标签节点 -->
        <mxCell id="tag1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="270" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="130" y="270" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- 连接边 -->
        <mxCell id="edge1" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="antenna1" target="tag1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge2" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="antenna2" target="tag2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge3" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="antenna1" target="tag2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge4" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="antenna3" target="tag3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="250" as="sourcePoint" />
            <mxPoint x="250" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 箭头指向注意力机制 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="235" as="sourcePoint" />
            <mxPoint x="280" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- GATv2注意力机制模块 -->
        <mxCell id="attention_module" value="GATv2 注意力机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="80" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 注意力计算过程 -->
        <mxCell id="attention_calc" value="注意力权重计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="140" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="feature_concat" value="特征拼接&#xa;[hᵢ || hⱼ || eᵢⱼ]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="300" y="180" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="linear_transform" value="线性变换&#xa;Wᵣ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="390" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="leaky_relu" value="LeakyReLU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="300" y="230" width="70" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="softmax" value="Softmax" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="380" y="230" width="60" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="attention_weight" value="αᵢⱼ⁽ʳ⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="270" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="attention_calc" target="feature_concat">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="feature_concat" target="leaky_relu">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="linear_transform" target="softmax">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="softmax" target="attention_weight">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="300" as="sourcePoint" />
            <mxPoint x="450" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 箭头指向多头注意力 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="235" as="sourcePoint" />
            <mxPoint x="560" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 多头注意力模块 -->
        <mxCell id="multihead_module" value="多头注意力聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="80" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 注意力头 -->
        <mxCell id="head1" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="580" y="140" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="head2" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="630" y="140" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="head3" value="Head 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="680" y="140" width="40" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="head_h1" value="h₁⁽ˡ⁺¹'¹⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="585" y="180" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="head_h2" value="h₁⁽ˡ⁺¹'²⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="635" y="180" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="head_h3" value="h₁⁽ˡ⁺¹'³⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="685" y="180" width="30" height="20" as="geometry" />
        </mxCell>
        
        <!-- 拼接操作 -->
        <mxCell id="concat" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="620" y="220" width="50" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="final_feature" value="h₁⁽ˡ⁺¹⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="625" y="260" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- 连接多头注意力 -->
        <mxCell id="conn_head1" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head1" target="head_h1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_head2" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head2" target="head_h2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_head3" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head3" target="head_h3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn_concat1" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head_h1" target="concat">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_concat2" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head_h2" target="concat">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_concat3" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="head_h3" target="concat">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn_final" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="concat" target="final_feature">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 箭头指向信号可靠性调节 -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="760" y="235" as="sourcePoint" />
            <mxPoint x="840" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 信号可靠性调节模块 -->
        <mxCell id="reliability_module" value="信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="80" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 可靠性评估网络 -->
        <mxCell id="reliability_net" value="可靠性评估网络" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="860" y="140" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="relu_layer" value="ReLU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="860" y="180" width="40" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="sigmoid_layer" value="Sigmoid" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="910" y="180" width="50" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="reliability_score" value="ρᵢ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="885" y="220" width="30" height="25" as="geometry" />
        </mxCell>
        
        <!-- 自适应调节 -->
        <mxCell id="adaptive_scaling" value="自适应缩放&#xa;αₗ + (1-αₗ)·ρᵢ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="860" y="260" width="100" height="35" as="geometry" />
        </mxCell>
        
        <!-- 连接可靠性调节 -->
        <mxCell id="conn_rel1" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="reliability_net" target="relu_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_rel2" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="relu_layer" target="sigmoid_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_rel3" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="sigmoid_layer" target="reliability_score">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_rel4" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="reliability_score" target="adaptive_scaling">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="300" as="sourcePoint" />
            <mxPoint x="1050" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 输出 -->
        <mxCell id="output" value="调节后特征&#xa;h⁽ˡ'ᵃᵈʲᵘˢᵗᵉᵈ⁾" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="320" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="conn_output" value="" style="endArrow=classic;html=1;strokeColor=#666666;" edge="1" parent="1" source="adaptive_scaling" target="output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1000" y="400" as="sourcePoint" />
            <mxPoint x="1050" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend_title" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="40" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend_antenna" value="天线节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="430" width="60" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend_tag" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="130" y="430" width="60" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend_attention" value="注意力权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffd966;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="210" y="430" width="70" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend_feature" value="特征向量" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="430" width="60" height="25" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
