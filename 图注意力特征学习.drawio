<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="600" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入信号嵌入部分 -->
        <mxCell id="input_embedding" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="150" width="120" height="80" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="RSSI信号嵌入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="120" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 多层信号表示 -->
        <mxCell id="signal1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="60" y="160" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCC99;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="85" y="160" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFB366;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="110" y="160" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9933;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="135" y="160" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="signal5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6CCFF;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="60" y="180" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal6" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D9B3FF;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="85" y="180" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal7" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#CC99FF;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="110" y="180" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal8" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B380FF;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="135" y="180" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="signal9" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#CCE5FF;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="60" y="200" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal10" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#99D6FF;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="85" y="200" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal11" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#66C7FF;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="110" y="200" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="signal12" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#33B8FF;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="135" y="200" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="input_label" value="RSSI信号特征向量" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="60" y="240" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 添加数学符号 -->
        <mxCell id="math_h" value="h_i, h_j" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="255" width="40" height="10" as="geometry" />
        </mxCell>

        <!-- 箭头指向注意力机制 -->
        <mxCell id="arrow_to_attention" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;fillColor=#000000;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="190" as="sourcePoint" />
            <mxPoint x="250" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 注意力机制模块 -->
        <mxCell id="attention_mechanism" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="270" y="80" width="200" height="220" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="330" y="50" width="100" height="20" as="geometry" />
        </mxCell>

        <!-- 顶部节点序列 -->
        <mxCell id="top_node1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="290" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="top_node2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="320" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="top_node3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="350" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="top_node4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="380" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="top_node5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="410" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="top_node6" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="440" y="90" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力计算区域 -->
        <mxCell id="attention_calc_area" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="285" y="130" width="170" height="60" as="geometry" />
        </mxCell>

        <!-- 注意力权重矩阵 -->
        <mxCell id="weight_matrix" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="295" y="140" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="weight_matrix2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="330" y="140" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="weight_matrix3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="365" y="140" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="weight_matrix4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="400" y="140" width="30" height="20" as="geometry" />
        </mxCell>

        <mxCell id="attention_label1" value="[h_i || h_j || e_ij]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="295" y="165" width="60" height="10" as="geometry" />
        </mxCell>
        <mxCell id="attention_label2" value="LeakyReLU + Softmax" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="365" y="165" width="80" height="10" as="geometry" />
        </mxCell>

        <!-- 注意力权重符号 -->
        <mxCell id="alpha_symbol" value="α_ij^(r)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="400" y="175" width="30" height="10" as="geometry" />
        </mxCell>

        <!-- 底部节点序列 -->
        <mxCell id="bottom_node1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="290" y="270" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bottom_node2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="320" y="270" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bottom_node3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="350" y="270" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bottom_node4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="380" y="270" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bottom_node5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="410" y="270" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="bottom_node6" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="440" y="270" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 连接线从顶部到注意力机制 -->
        <mxCell id="conn_top1" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=1;" edge="1" parent="1" source="top_node1" target="weight_matrix">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_top2" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=1;" edge="1" parent="1" source="top_node2" target="weight_matrix2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_top3" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=1;" edge="1" parent="1" source="top_node3" target="weight_matrix3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_top4" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=1;" edge="1" parent="1" source="top_node4" target="weight_matrix4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 连接线从注意力机制到底部 -->
        <mxCell id="conn_bottom1" value="" style="endArrow=none;html=1;strokeColor=#B85450;strokeWidth=1;" edge="1" parent="1" source="weight_matrix" target="bottom_node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_bottom2" value="" style="endArrow=none;html=1;strokeColor=#B85450;strokeWidth=1;" edge="1" parent="1" source="weight_matrix2" target="bottom_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_bottom3" value="" style="endArrow=none;html=1;strokeColor=#B85450;strokeWidth=1;" edge="1" parent="1" source="weight_matrix3" target="bottom_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn_bottom4" value="" style="endArrow=none;html=1;strokeColor=#B85450;strokeWidth=1;" edge="1" parent="1" source="weight_matrix4" target="bottom_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头指向图网络 -->
        <mxCell id="arrow_to_graph" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;fillColor=#000000;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="190" as="sourcePoint" />
            <mxPoint x="550" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图网络结构 -->
        <mxCell id="graph_network" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="570" y="80" width="200" height="220" as="geometry" />
        </mxCell>

        <mxCell id="graph_title" value="异构图结构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="620" y="50" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 上层图结构 -->
        <mxCell id="graph_node1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="590" y="100" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="graph_node2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="620" y="100" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="graph_node3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="650" y="100" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="graph_node4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="680" y="100" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="graph_node5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="710" y="100" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="graph_node6" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="740" y="100" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 中心图结构 -->
        <mxCell id="center_graph1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="605" y="140" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="center_graph2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="635" y="140" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="center_graph3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="665" y="140" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="center_graph4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="695" y="140" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="center_graph5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="725" y="140" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 下层图结构 -->
        <mxCell id="lower_graph1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="590" y="180" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="lower_graph2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="620" y="180" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="lower_graph3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="650" y="180" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="lower_graph4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="680" y="180" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="lower_graph5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="710" y="180" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="lower_graph6" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="740" y="180" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 图网络连接线 -->
        <mxCell id="graph_conn1" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="graph_node1" target="center_graph1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="200" as="sourcePoint" />
            <mxPoint x="850" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="graph_conn2" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="graph_node2" target="center_graph2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="200" as="sourcePoint" />
            <mxPoint x="850" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="graph_conn3" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="center_graph1" target="lower_graph1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="200" as="sourcePoint" />
            <mxPoint x="850" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="graph_conn4" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="center_graph2" target="lower_graph2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="200" as="sourcePoint" />
            <mxPoint x="850" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="graph_conn5" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="1" source="center_graph3" target="lower_graph3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="200" as="sourcePoint" />
            <mxPoint x="850" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力聚合标签 -->
        <mxCell id="multihead_label" value="Concat(h_i^(l+1,1), h_i^(l+1,2), ..., h_i^(l+1,H))" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="580" y="220" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- 残差连接标签 -->
        <mxCell id="residual_label" value="残差连接机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="620" y="250" width="80" height="12" as="geometry" />
        </mxCell>

        <!-- 箭头指向输出 -->
        <mxCell id="arrow_to_output" value="" style="endArrow=classic;html=1;strokeColor=#000000;strokeWidth=2;fillColor=#000000;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="190" as="sourcePoint" />
            <mxPoint x="850" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出模块 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="870" y="150" width="120" height="80" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="节点特征更新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="900" y="120" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 特征更新公式 -->
        <mxCell id="update_formula" value="h_i^(l+1) = σ(Σ_r Σ_j α_ij^(r) W_r^(l) h_j^(l))" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="880" y="170" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- ELU激活函数标签 -->
        <mxCell id="elu_label" value="ELU激活函数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="880" y="210" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 节点类型标签 -->
        <mxCell id="antenna_label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="280" y="320" width="50" height="15" as="geometry" />
        </mxCell>
        <mxCell id="antenna_example" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="260" y="320" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="tag_label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="380" y="320" width="50" height="15" as="geometry" />
        </mxCell>
        <mxCell id="tag_example" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="360" y="320" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 图标题 -->
        <mxCell id="figure_title" value="图 4. GATv2异构图注意力机制架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#FF6600;" vertex="1" parent="1">
          <mxGeometry x="450" y="350" width="250" height="20" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
